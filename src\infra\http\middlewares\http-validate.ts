import { ZodType } from "zod";
import { FastifyReply, FastifyRequest } from "fastify";
import { fromZodError } from "zod-validation-error";
import { BadRequestError } from "@/core/errors/bad-request.error";

export const httpValidate =
  (schema: ZodType<any>) => async (req: FastifyRequest, _: FastifyReply) => {
    const result = schema.safeParse({
      body: req.body,
      query: req.query,
      params: req.params,
    });

    if (!result.success) {
      throw new BadRequestError(
        fromZodError(result.error, {
          includePath: false,
          forceTitleCase: false,
          issueSeparator: ", ",
          prefix: null,
          prefixSeparator: "",
        }).toString()
      );
    }

    req.body = result.data.body;
    req.query = result.data.query;
    req.params = result.data.params;
  };
