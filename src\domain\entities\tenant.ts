import { BaseEntity } from "@/core/entities/base-entity";
import { UniqueEntityObjectId } from "@/core/entities/unique-entity-objectId";
import { Optional } from "@/core/types/optional";
import { ITimestamp } from "@/core/types/timestamp";

export type TenantProps = {
  name: String;
} & ITimestamp;

export class Tenant extends BaseEntity<TenantProps> {
  private touch() {
    this.props.updatedAt = new Date();
  }

  get name() {
    return this.props.name;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Date {
    return this.props.updatedAt;
  }

  changeName(name: string) {
    this.props.name = name;
    this.touch();
  }

  static create(
    props: Optional<TenantProps, "createdAt" | "updatedAt">,
    id?: UniqueEntityObjectId
  ) {
    const tenant = new Tenant(
      {
        ...props,
        createdAt: props.createdAt ?? new Date(),
        updatedAt: props.updatedAt ?? new Date(),
      },
      id
    );
    return tenant;
  }
}
