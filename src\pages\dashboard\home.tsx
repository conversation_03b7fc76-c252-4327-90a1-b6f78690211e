import { Helmet } from 'react-helmet-async';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { CONFIG } from 'src/global-config';
import { DashboardContent } from 'src/layouts/dashboard';

// ----------------------------------------------------------------------

const metadata = { title: `Página Inicial - ${CONFIG.appName}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title> {metadata.title}</title>
      </Helmet>

      <DashboardContent>
        <Container maxWidth="md">
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h3" sx={{ mb: 2 }}>
              Sistema DNS Sinkhole
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Gerenciamento automatizado de políticas de acesso DNS
            </Typography>
          </Box>

          <Card sx={{ p: 4 }}>
            <Typography variant="h5" sx={{ mb: 3, textAlign: 'center' }}>
              Sobre o Projeto
            </Typography>

            <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
              Este sistema automatiza a implantação e gestão de servidores DNS Sinkhole, permitindo
              o controle centralizado de políticas de acesso à internet em diferentes sub-redes
              organizacionais.
            </Typography>

            <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
              Utilizando tecnologias como <strong>DNSmasq</strong> para o DNS Sinkhole,
              <strong> Ansible</strong> para automação de configurações e uma interface web para
              gerenciamento, o sistema oferece uma solução escalável para bloqueio e liberação de
              domínios específicos.
            </Typography>

            <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
              Ideal para instituições de ensino, ambientes corporativos e qualquer organização que
              necessite de controle granular sobre o acesso à internet, garantindo segurança e
              produtividade através de políticas personalizáveis.
            </Typography>
          </Card>
        </Container>
      </DashboardContent>
    </>
  );
}
