import mongoose from "mongoose";
import { z, ZodString } from "zod";

// Augmenta o tipo do Zod para incluir nosso método
declare module "zod" {
  interface ZodString {
    objectId(message?: string): ZodString;
  }
}

// Implementação do método
ZodString.prototype.objectId = function (message = "Id inválido") {
  return this.refine(
    (val: any) =>
      val === null ||
      val === undefined ||
      val === "" ||
      mongoose.Types.ObjectId.isValid(val),
    { message }
  );
};
