import { inject, injectable } from "tsyringe";
import { FastifyReply, FastifyRequest } from "fastify";
import { TenantPresenter } from "@/infra/http/presenters/tenant.presenter";
import { FindManyTenantsPaginatedService } from "@/domain/services/tenant/find-many-tenants-paginated.service";
import { FindTenantByIdService } from "@/domain/services/tenant/find-tenant-by-id.service";
import { CreateTenantService } from "@/domain/services/tenant/create-tenant.service";
import { UpdateTenantService } from "@/domain/services/tenant/update-tenant.service";
import { TenantFastifyRequest } from "@/infra/http/routes/tenant.router";

@injectable()
export class TenantController {
  constructor(
    @inject(FindManyTenantsPaginatedService)
    private readonly findManyTenantsPaginatedService: FindManyTenantsPaginatedService,
    @inject(FindTenantByIdService)
    private readonly findTenantByIdService: FindTenantByIdService,
    @inject(CreateTenantService)
    private readonly createTenantService: CreateTenantService,
    @inject(UpdateTenantService)
    private readonly updateTenantService: UpdateTenantService
  ) {}

  list = async (
    req: FastifyRequest<TenantFastifyRequest.List>,
    reply: FastifyReply
  ) => {
    const { metadata } = await this.findManyTenantsPaginatedService.execute({
      filters: {
        page: req.query.page,
        limit: req.query.limit,
      },
    });

    return reply.status(200).send({
      page: metadata.page,
      limit: metadata.limit,
      hasMore: metadata.hasMore,
      items: metadata.data.map(TenantPresenter.toHttp),
    });
  };

  show = async (
    req: FastifyRequest<TenantFastifyRequest.Show>,
    reply: FastifyReply
  ) => {
    const { tenant } = await this.findTenantByIdService.execute({
      id: req.params.tenantId,
    });

    return reply.status(200).send(TenantPresenter.toHttp(tenant));
  };

  create = async (
    req: FastifyRequest<TenantFastifyRequest.Create>,
    reply: FastifyReply
  ) => {
    const { tenant } = await this.createTenantService.execute({
      name: req.body.name,
    });

    return reply.status(201).send(TenantPresenter.toHttp(tenant));
  };

  update = async (
    req: FastifyRequest<TenantFastifyRequest.Update>,
    reply: FastifyReply
  ) => {
    const { tenant } = await this.updateTenantService.execute({
      id: req.params.tenantId,
      name: req.body.name,
    });

    return reply.status(200).send(TenantPresenter.toHttp(tenant));
  };
}
