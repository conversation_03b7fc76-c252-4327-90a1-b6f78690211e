import { inject, injectable } from "tsyringe";
import { Tenant } from "@/domain/entities/tenant";
import { UpdateTenantUseCase } from "@/domain/use-cases/tenant/update-tenant.use-case";

export interface IUpdateTenantServiceRequest {
  id: string;
  name?: string;
}

export interface IUpdateTenantServiceResponse {
  tenant: Tenant;
}

@injectable()
export class UpdateTenantService {
  constructor(
    @inject(UpdateTenantUseCase)
    private readonly updateTenantUseCase: UpdateTenantUseCase
  ) {}

  async execute({
    id,
    name,
  }: IUpdateTenantServiceRequest): Promise<IUpdateTenantServiceResponse> {
    const { tenant } = await this.updateTenantUseCase.execute({ id, name });

    return { tenant };
  }
}
