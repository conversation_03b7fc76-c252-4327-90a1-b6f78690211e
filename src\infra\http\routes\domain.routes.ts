import { container } from "tsyringe";
import { FastifyInstance } from "fastify";
import { httpValidate } from "@/infra/http/middlewares/http-validate";
import { DomainController } from "@/infra/http/controllers/domain.controller";
import { DomainValidation } from "@/infra/http/validations/domain.validation";

export async function domainRoutes(app: FastifyInstance) {
  const controller = container.resolve<DomainController>(DomainController);

  app.post<DomainFastifyRequest.HandleDomainList>(
    "/",
    {
      preHandler: [httpValidate(DomainValidation.handleDomainList)],
    },
    controller.handleDomainList
  );

  app.get("/config", controller.readDomainConfig);
}

export namespace DomainFastifyRequest {
  export interface HandleDomainList {
    Body: {
      isBlocked: boolean;
      domains: { url: string }[];
    };
  }
}
