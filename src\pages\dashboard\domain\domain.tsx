import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useBoolean } from 'minimal-shared/hooks';
import { useMutation } from '@tanstack/react-query';
import { useFieldArray, useForm } from 'react-hook-form';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Card,
  Chip,
  Container,
  Divider,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';
import { DomainRepository } from 'src/repositories/domain-repository';

import { Iconify } from 'src/components/iconify';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

const metadata = { title: `Regras de Acesso - ${CONFIG.appName}` };

interface DomainItem {
  url: string;
}

interface FormData {
  isBlocked: boolean;
  domains: DomainItem[];
}

export default function HostPage() {
  const [newDomainUrl, setNewDomainUrl] = useState('');
  const isBlocked = useBoolean(false);

  const { control, watch, setValue } = useForm<FormData>({
    defaultValues: {
      domains: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'domains',
  });

  const domains = watch('domains') || [];

  const handleAddDomain = () => {
    if (newDomainUrl.trim()) {
      append({ url: newDomainUrl.trim() });
      setNewDomainUrl('');
    }
  };

  const handleRemoveDomain = (index: number) => {
    remove(index);
  };

  const domainMutation = useMutation({
    mutationFn: async (data: FormData) => {
      DomainRepository.handleDomainList(data);
    },
  });

  const handleSave = () => {
    const formData = watch();
    domainMutation.mutate({ ...formData, isBlocked: isBlocked.value });
  };

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
        <meta name="description" content={metadata.title} />
      </Helmet>

      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Regras de Acesso"
          links={[{ name: 'Início', href: paths.dashboard.root }, { name: 'Lista' }]}
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Stack spacing={3}>
          <Card sx={{ p: 3 }}>
            <Stack
              direction={{ xs: 'column', md: 'row' }}
              spacing={3}
              alignItems={{ xs: 'flex-start', md: 'center' }}
              justifyContent="space-between"
            >
              <Stack spacing={1} sx={{ flex: 1 }}>
                <Typography variant="h4">Política Padrão</Typography>
              </Stack>

              <Stack direction="row" spacing={2} alignItems="center">
                <Alert
                  severity={isBlocked.value ? 'error' : 'success'}
                  variant="outlined"
                  sx={{
                    borderRadius: 1.5,
                    py: 0.5,
                    px: 2,
                    '& .MuiAlert-message': {
                      fontSize: '0.875rem',
                      fontWeight: 500,
                    },
                  }}
                >
                  {isBlocked.value
                    ? 'Bloqueando todos os domínios por padrão'
                    : 'Liberando todos os domínios por padrão'}
                </Alert>

                <Button
                  variant={isBlocked.value ? 'outlined' : 'contained'}
                  color={isBlocked.value ? 'success' : 'error'}
                  startIcon={
                    <Iconify icon={isBlocked.value ? 'eva:unlock-fill' : 'eva:lock-fill'} />
                  }
                  onClick={isBlocked.onToggle}
                  sx={{
                    minWidth: 140,
                    height: 40,
                    borderRadius: 1.5,
                    fontWeight: 600,
                  }}
                >
                  {isBlocked.value ? 'Liberar Todos os Domínios' : 'Bloquear Todos os Domínios'}
                </Button>
              </Stack>
            </Stack>
          </Card>

          <Card sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
              Lista de Exceções
            </Typography>
            <Stack spacing={3} pb={3}>
              <Stack spacing={1}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="eva:plus-circle-fill" sx={{ color: 'primary.main' }} />
                  Adicionar Novo Domínio
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Digite o domínio que deseja adicionar à lista de controle
                </Typography>
              </Stack>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <TextField
                  fullWidth
                  label="URL do Domínio"
                  value={newDomainUrl}
                  onChange={(e) => setNewDomainUrl(e.target.value)}
                  placeholder="Exemplo: https://google.com, https://youtube.com, https://facebook.com"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleAddDomain();
                    }
                  }}
                  sx={{
                    flex: 1,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <Iconify icon="eva:globe-2-outline" sx={{ color: 'text.disabled', mr: 1 }} />
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  startIcon={<Iconify icon="eva:plus-fill" />}
                  onClick={handleAddDomain}
                  disabled={!newDomainUrl.trim()}
                  sx={{
                    minWidth: 140,
                    height: 56,
                    borderRadius: 1.5,
                    fontWeight: 600,
                  }}
                >
                  Adicionar
                </Button>
              </Stack>
            </Stack>

            <Stack spacing={3}>
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={2}
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                justifyContent="space-between"
              >
                <Stack spacing={0.5}>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Iconify icon="eva:list-fill" sx={{ color: 'primary.main' }} />
                    Lista de Domínios ({domains.length})
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Gerencie o status de bloqueio de cada domínio
                  </Typography>
                </Stack>
              </Stack>

              <Divider />

              {fields.length > 0 ? (
                <TableContainer
                  component={Paper}
                  variant="outlined"
                  sx={{
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'divider',
                    overflow: 'hidden',
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'grey.50' }}>
                        <TableCell sx={{ fontWeight: 600, py: 2 }}>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Iconify icon="eva:globe-outline" />
                            <Typography>Domínio</Typography>
                          </Stack>
                        </TableCell>
                        <TableCell align="center" sx={{ fontWeight: 600, py: 2 }}>
                          <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="center"
                            spacing={1}
                          >
                            <Iconify icon="eva:shield-outline" />
                            <Typography>Status</Typography>
                          </Stack>
                        </TableCell>
                        <TableCell align="center" sx={{ fontWeight: 600, py: 2 }}>
                          <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="center"
                            spacing={1}
                          >
                            <Iconify icon="eva:settings-outline" />
                            <Typography>Ações</Typography>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {fields.map((field, index) => (
                        <TableRow
                          key={field.id}
                          sx={{
                            '&:last-child td, &:last-child th': { border: 0 },
                            transition: 'background-color 0.2s ease',
                          }}
                        >
                          <TableCell sx={{ py: 2 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                              <Box
                                sx={{
                                  width: 32,
                                  height: 32,
                                  borderRadius: 1,
                                  backgroundColor: isBlocked.value
                                    ? 'success.lighter'
                                    : 'error.lighter',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <Iconify
                                  icon={isBlocked.value ? 'eva:checkmark-fill' : 'eva:close-fill'}
                                  sx={{
                                    color: isBlocked.value ? 'success.main' : 'error.main',
                                    width: 16,
                                    height: 16,
                                  }}
                                />
                              </Box>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: 'monospace',
                                  fontSize: '0.875rem',
                                  fontWeight: 500,
                                }}
                              >
                                {domains[index]?.url || ''}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell align="center" sx={{ py: 2 }}>
                            <Chip
                              label={isBlocked.value ? 'Liberado' : 'Bloqueado'}
                              color={isBlocked.value ? 'success' : 'error'}
                              size="small"
                              sx={{
                                cursor: 'pointer',
                                minWidth: 90,
                                fontWeight: 600,
                                borderRadius: 1,
                                '&:hover': {
                                  transform: 'scale(1.02)',
                                },
                                transition: 'transform 0.2s ease',
                              }}
                            />
                          </TableCell>
                          <TableCell align="center" sx={{ py: 2 }}>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRemoveDomain(index)}
                              sx={{
                                borderRadius: 1,
                                '&:hover': {
                                  backgroundColor: 'error.lighter',
                                  transform: 'scale(1.1)',
                                },
                                transition: 'all 0.2s ease',
                              }}
                            >
                              <Iconify icon="eva:trash-2-outline" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Paper
                  variant="outlined"
                  sx={{
                    p: 6,
                    textAlign: 'center',
                    borderRadius: 2,
                    borderStyle: 'dashed',
                  }}
                >
                  <Stack spacing={2} alignItems="center">
                    <Box
                      sx={{
                        width: 64,
                        height: 64,
                        borderRadius: 2,
                        backgroundColor: 'primary.lighter',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Iconify
                        icon="eva:globe-2-outline"
                        sx={{ color: 'primary.main', width: 32, height: 32 }}
                      />
                    </Box>
                    <Stack spacing={1}>
                      <Typography variant="h6" color="text.primary">
                        Nenhum domínio adicionado
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Use o formulário acima para adicionar seu primeiro domínio à lista
                      </Typography>
                    </Stack>
                  </Stack>
                </Paper>
              )}

              <Divider />

              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={2}
                alignItems={{ xs: 'stretch', sm: 'center' }}
                justifyContent="end"
              >
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Iconify icon="eva:save-fill" />}
                  onClick={handleSave}
                  disabled={domains.length === 0}
                  sx={{
                    minWidth: 200,
                    borderRadius: 1.5,
                    fontWeight: 600,
                    py: 1.5,
                  }}
                >
                  Salvar Configurações
                </Button>
              </Stack>
            </Stack>
          </Card>
        </Stack>
      </Container>
    </>
  );
}
