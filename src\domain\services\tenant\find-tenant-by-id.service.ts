import { inject, injectable } from "tsyringe";
import { Tenant } from "@/domain/entities/tenant";
import { FindTenantByIdUseCase } from "@/domain/use-cases/tenant/find-tenant-by-id.use-case";

export interface IFindTenantByIdServiceRequest {
  id: string;
}

export interface IFindTenantByIdServiceResponse {
  tenant: Tenant;
}

@injectable()
export class FindTenantByIdService {
  constructor(
    @inject(FindTenantByIdUseCase)
    private readonly findTenantByIdUseCase: FindTenantByIdUseCase
  ) {}

  async execute({
    id,
  }: IFindTenantByIdServiceRequest): Promise<IFindTenantByIdServiceResponse> {
    const { tenant } = await this.findTenantByIdUseCase.execute({
      id,
    });

    return { tenant };
  }
}
