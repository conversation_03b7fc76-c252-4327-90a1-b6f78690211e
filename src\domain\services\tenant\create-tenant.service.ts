import { inject, injectable } from "tsyringe";
import { Tenant } from "@/domain/entities/tenant";
import { CreateTenantUseCase } from "@/domain/use-cases/tenant/create-tenant.use-case";

export interface ICreateTenantServiceRequest {
  name: string;
}

export interface ICreateTenantServiceResponse {
  tenant: Tenant;
}

@injectable()
export class CreateTenantService {
  constructor(
    @inject(CreateTenantUseCase)
    private readonly createTenantUseCase: CreateTenantUseCase
  ) {}

  async execute({
    name,
  }: ICreateTenantServiceRequest): Promise<ICreateTenantServiceResponse> {
    const { tenant } = await this.createTenantUseCase.execute({
      name,
    });

    return { tenant };
  }
}
