{"name": "tcc-controle-de-acesso", "author": "<PERSON><PERSON>", "version": "1.0.0", "description": "Controle de Acesso à Domínios Específicos", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "tsc && vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "lint:print": "npx eslint --print-config eslint.config.mjs > eslint-show-config.json", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "clean": "rm -rf node_modules .next out dist build", "re:dev": "yarn clean && yarn install && yarn dev", "re:build": "yarn clean && yarn install && yarn build", "re:build-npm": "npm run clean && npm install && npm run build", "tsc:dev": "yarn dev & yarn tsc:watch", "tsc:watch": "tsc --noEmit --watch", "tsc:print": "npx tsc --showConfig"}, "engines": {"node": "20.x"}, "packageManager": "yarn@1.22.22", "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource-variable/dm-sans": "^5.1.1", "@fontsource-variable/inter": "^5.1.1", "@fontsource-variable/nunito-sans": "^5.1.1", "@fontsource-variable/public-sans": "^5.1.2", "@fontsource/barlow": "^5.1.1", "@hookform/resolvers": "^5.2.2", "@iconify/react": "^5.1.0", "@mui/lab": "^6.0.0-beta.21", "@mui/material": "^6.3.0", "@mui/x-data-grid": "^7.23.5", "@mui/x-date-pickers": "^7.23.3", "@mui/x-tree-view": "^7.23.2", "@tanstack/react-query": "^5.90.2", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.9", "dayjs": "^1.11.13", "es-toolkit": "^1.31.0", "framer-motion": "^11.15.0", "minimal-shared": "^1.0.5", "nprogress": "^0.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.62.0", "react-router": "^7.1.1", "simplebar-react": "^3.3.0", "stylis": "^4.3.4", "stylis-plugin-rtl": "^2.1.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/autosuggest-highlight": "^3.2.3", "@types/node": "^22.10.3", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/stylis": "^4.2.7", "@typescript-eslint/parser": "^8.19.0", "@vitejs/plugin-react-swc": "^3.7.2", "eslint": "^9.17.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.4.0", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "typescript-eslint": "^8.19.0", "vite": "^6.0.6", "vite-plugin-checker": "^0.8.0"}}