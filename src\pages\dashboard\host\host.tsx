import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useBoolean } from 'minimal-shared/hooks';
import { useFieldArray, useForm } from 'react-hook-form';

import {
  Box,
  <PERSON><PERSON>,
  Card,
  Container,
  Divider,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

const metadata = { title: `Domínios - ${CONFIG.appName}` };

interface HostItem {
  ip: string;
}

interface FormData {
  hosts: HostItem[];
  dnsIp: string;
  sshPort: string;
}

export default function HostPage() {
  const [newHostIp, setNewHostIp] = useState('');
  const isBlocked = useBoolean(false);

  const { control, watch, setValue, register } = useForm<FormData>({
    defaultValues: {
      hosts: [],
      dnsIp: '',
      sshPort: '22',
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'hosts',
  });

  const hosts = watch('hosts') || [];

  const handleAddHost = () => {
    if (newHostIp.trim()) {
      append({ ip: newHostIp.trim() });
      setNewHostIp('');
    }
  };

  const handleRemoveHost = (index: number) => {
    remove(index);
  };

  const handleIpChange = (index: number, value: string) => {
    setValue(`hosts.${index}.ip`, value);
  };

  const handleSave = () => {
    const formData = watch();
    const configData = {
      hosts: formData.hosts,
      networkConfig: {
        dnsIp: formData.dnsIp || '',
        sshPort: formData.sshPort || '22',
      },
    };
    console.log('Dados salvos:', configData);
    // Aqui você pode enviar os dados para o backend
    // exemplo: await api.post('/hosts/config', configData);
  };

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
        <meta name="description" content={metadata.title} />
      </Helmet>

      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Dispositivos"
          links={[{ name: 'Início', href: paths.dashboard.root }, { name: 'Dispositivos' }]}
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Stack spacing={3}>
          <Card sx={{ p: 3 }}>
            <Stack
              direction={{ xs: 'column', md: 'row' }}
              spacing={3}
              alignItems={{ xs: 'flex-start', md: 'center' }}
              justifyContent="space-between"
            >
              <Stack spacing={1} sx={{ flex: 1 }}>
                <Typography variant="h4" gutterBottom>
                  Gerenciamento de Dispositivos
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Adicione e gerencie dispositivos para controlar o acesso e bloqueio de sites
                </Typography>
              </Stack>
            </Stack>
          </Card>

          <Card sx={{ p: 3 }}>
            <Stack spacing={3}>
              <Stack spacing={1}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="eva:settings-2-fill" sx={{ color: 'primary.main' }} />
                  Configurações de Rede
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Configure o DNS e a porta SSH para os dispositivos
                </Typography>
              </Stack>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <TextField
                  fullWidth
                  label="IP do DNS"
                  placeholder="Exemplo: *******, *******"
                  helperText="IP da máquina onde o DNSmasq está rodando"
                  {...register('dnsIp')}
                  sx={{
                    flex: 1,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <Iconify icon="eva:wifi-outline" sx={{ color: 'text.disabled', mr: 1 }} />
                    ),
                  }}
                />
                <TextField
                  label="Porta SSH"
                  placeholder="22"
                  {...register('sshPort')}
                  sx={{
                    minWidth: 140,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <Iconify icon="eva:lock-outline" sx={{ color: 'text.disabled', mr: 1 }} />
                    ),
                  }}
                />
              </Stack>
            </Stack>
          </Card>

          <Card sx={{ p: 3 }}>
            <Stack spacing={3}>
              <Stack spacing={1}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="eva:plus-circle-fill" sx={{ color: 'primary.main' }} />
                  Adicionar Novo Dispositivo
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Digite o endereço IP que deseja adicionar ao inventário
                </Typography>
              </Stack>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <TextField
                  fullWidth
                  label="IP do Host"
                  value={newHostIp}
                  onChange={(e) => setNewHostIp(e.target.value)}
                  placeholder="Exemplo: ***********, ********, **********"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleAddHost();
                    }
                  }}
                  sx={{
                    flex: 1,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <Iconify icon="eva:globe-2-outline" sx={{ color: 'text.disabled', mr: 1 }} />
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  startIcon={<Iconify icon="eva:plus-fill" />}
                  onClick={handleAddHost}
                  disabled={!newHostIp.trim()}
                  sx={{
                    minWidth: 140,
                    height: 56,
                    borderRadius: 1.5,
                    fontWeight: 600,
                  }}
                >
                  Adicionar
                </Button>
              </Stack>
            </Stack>
          </Card>

          <Card sx={{ p: 3, mb: 5 }}>
            <Stack spacing={3}>
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={2}
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                justifyContent="space-between"
              >
                <Stack spacing={0.5}>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Iconify icon="eva:list-fill" sx={{ color: 'primary.main' }} />
                    Lista de Dispositivos Gerenciados ({hosts.length})
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Gerencie os endereços IP dos dispositivos gerenciados
                  </Typography>
                </Stack>
              </Stack>

              <Divider />

              {fields.length > 0 ? (
                <TableContainer
                  component={Paper}
                  variant="outlined"
                  sx={{
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'divider',
                    overflow: 'hidden',
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'grey.50' }}>
                        <TableCell sx={{ fontWeight: 600, py: 2 }}>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Iconify icon="eva:globe-outline" />
                            <Typography>Host</Typography>
                          </Stack>
                        </TableCell>
                        <TableCell align="center" sx={{ fontWeight: 600, py: 2 }}>
                          <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="center"
                            spacing={1}
                          >
                            <Iconify icon="eva:settings-outline" />
                            <Typography>Ações</Typography>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {fields.map((field, index) => (
                        <TableRow
                          key={field.id}
                          sx={{
                            '&:last-child td, &:last-child th': { border: 0 },
                            transition: 'background-color 0.2s ease',
                          }}
                        >
                          <TableCell sx={{ py: 2 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                              <Box
                                sx={{
                                  width: 32,
                                  height: 32,
                                  borderRadius: 1,
                                  backgroundColor: 'success.lighter',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <Iconify
                                  icon="eva:globe-2-outline"
                                  sx={{
                                    color: 'success.main',
                                    width: 16,
                                    height: 16,
                                  }}
                                />
                              </Box>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: 'monospace',
                                  fontSize: '0.875rem',
                                  fontWeight: 500,
                                }}
                              >
                                {hosts[index]?.ip || ''}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell align="center" sx={{ py: 2 }}>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRemoveHost(index)}
                              sx={{
                                borderRadius: 1,
                                '&:hover': {
                                  backgroundColor: 'error.lighter',
                                  transform: 'scale(1.1)',
                                },
                                transition: 'all 0.2s ease',
                              }}
                            >
                              <Iconify icon="eva:trash-2-outline" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Paper
                  variant="outlined"
                  sx={{
                    p: 6,
                    textAlign: 'center',
                    borderRadius: 2,
                    borderStyle: 'dashed',
                  }}
                >
                  <Stack spacing={2} alignItems="center">
                    <Box
                      sx={{
                        width: 64,
                        height: 64,
                        borderRadius: 2,
                        backgroundColor: 'primary.lighter',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Iconify
                        icon="eva:globe-2-outline"
                        sx={{ color: 'primary.main', width: 32, height: 32 }}
                      />
                    </Box>
                    <Stack spacing={1}>
                      <Typography variant="h6" color="text.primary">
                        Nenhum dispositivo adicionado
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Use o formulário acima para adicionar seu primeiro dispositivo a ser
                        gerenciado
                      </Typography>
                    </Stack>
                  </Stack>
                </Paper>
              )}

              <Divider />

              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={2}
                alignItems={{ xs: 'stretch', sm: 'center' }}
                justifyContent="end"
              >
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Iconify icon="eva:save-fill" />}
                  onClick={handleSave}
                  disabled={hosts.length === 0 && !watch('dnsIp') && !watch('sshPort')}
                  sx={{
                    minWidth: 200,
                    borderRadius: 1.5,
                    fontWeight: 600,
                    py: 1.5,
                  }}
                >
                  Salvar Configurações
                </Button>
              </Stack>
            </Stack>
          </Card>
        </Stack>
      </Container>
    </>
  );
}
