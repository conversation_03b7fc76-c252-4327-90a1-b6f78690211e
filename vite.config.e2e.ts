import swc from "unplugin-swc";
import tsConf from "vite-tsconfig-paths";
import { defineConfig } from "vitest/config";

export default defineConfig({
  esbuild: false,
  plugins: [
    tsConf(),
    swc.vite({
      module: { type: "es6" },
    }),
  ],

  test: {
    globals: true,

    fileParallelism: false,
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },

    include: ["src/**/*.e2e-spec.ts"],
    exclude: ["node_modules/**", "dist/**"],

    setupFiles: ["tests/utils/setup.e2e.ts"],
  },
});
