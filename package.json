{"name": "billing", "packageManager": "yarn@4.9.2", "devDependencies": {"@faker-js/faker": "^10.0.0", "@swc/core": "^1.13.5", "@types/bcrypt": "^6", "@types/jsonwebtoken": "^9.0.10", "@types/module-alias": "^2.0.4", "@types/node": "^24.5.1", "pino-pretty": "^13.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2", "unplugin-swc": "^1.5.7", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "scripts": {"build": "tsc", "start": "node dist/src/infra/main.js", "test": "dotenv -e .env.test -- vitest run --config ./vite.config.ts", "test:e2e": "dotenv -e .env.test -- vitest run --config ./vite.config.e2e.ts", "dev": "dotenv -e .env.development -- ts-node-dev --exit-child -r tsconfig-paths/register --respawn --transpile-only --ignore node_modules ./src/infra/main.ts", "db:seed:dev": "dotenv -e .env.development -- ts-node --transpile-only -r tsconfig-paths/register --ignore node_modules ./src/infra/database/mongoose/seed.ts"}, "dependencies": {"@fastify/cors": "^11.1.0", "@typegoose/typegoose": "^12.19.0", "bcrypt": "^6.0.0", "dayjs": "^1.11.18", "fastify": "^5.6.0", "http-graceful-shutdown": "^3.1.14", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "mongoose": "^8.18.1", "pino": "^9.9.5", "tsconfig-paths": "^4.2.0", "tsyringe": "^4.10.0", "zod": "^4.1.9", "zod-validation-error": "^4.0.1"}}