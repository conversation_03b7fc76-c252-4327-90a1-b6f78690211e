import "reflect-metadata";
import "module-alias/register";
import "src/infra/utils/module-alias"; // this must be the first import and without alias

import * as z from "zod";
import { pt } from "zod/locales";

z.config(pt()); // Set Zod error messages to Portuguese

import "@/infra/container";
import "@/core/utils/zod-methods";

import { env } from "@/infra/env";
import { container } from "tsyringe";
import { MongooseDataSource } from "@/infra/database/mongoose/data-source";

async function initializeMongooseDatabase() {
  if (env.NODE_ENV !== "test") {
    throw new Error(
      "SEU MALUCO TÁ TENTANDO RODAR TESTES EM AMBIENTE DE PRODUÇÃO?"
    );
  }

  await MongooseDataSource.connect();
}

beforeAll(async () => {
  await initializeMongooseDatabase();
});

afterAll(async () => {
  await MongooseDataSource.cleanDatabase();
});

afterEach(async () => {
  container.clearInstances();
});
