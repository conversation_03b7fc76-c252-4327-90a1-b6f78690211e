import { JwtCrypter } from "@/infra/cryptography";
import { Tenant } from "@/domain/entities/tenant";
import { Account } from "@/domain/entities/account";
import { ITenantRepository } from "@/domain/repositories";
import { IAccountRepository } from "@/domain/repositories/account.repository";
import { container } from "tsyringe";
import { DependencyInjectionEnum } from "@/core/enums/dependency-injection";
import { makeTenant } from "@/tests/factories/tenant";
import { makeAccount } from "@/tests/factories/account";
import { app } from "@/infra/app";
import { TokenType } from "@/core/enums/token";
import { TenantPresenter } from "@/infra/http/presenters/tenant.presenter";
import { UniqueEntityObjectId } from "@/core/entities/unique-entity-objectId";

let jwtCrypter: JwtCrypter;
let tenantRepository: ITenantRepository;
let accountRepository: IAccountRepository;

let tenant: Tenant;
let account: Account;

describe("TenantController (e2e)", () => {
  beforeEach(async () => {
    jwtCrypter = container.resolve(JwtCrypter);

    tenantRepository = container.resolve(
      DependencyInjectionEnum.TENANT_REPOSITORY
    );

    accountRepository = container.resolve(
      DependencyInjectionEnum.ACCOUNT_REPOSITORY
    );

    tenant = await tenantRepository.create(makeTenant());
    account = await accountRepository.create(makeAccount());
  });

  describe("GET /admin/tenants", () => {
    it("should be able to list all tenants (200)", async () => {
      const tenant2 = await tenantRepository.create(makeTenant());

      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toValue() },
        TokenType.ACCESS_TOKEN
      );

      const response1 = await app.inject({
        method: "GET",
        url: PATHS.root,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      expect(response1.statusCode).toBe(200);
      expect(response1.json()).toStrictEqual({
        page: 1,
        limit: 100,
        hasMore: false,
        items: [tenant, tenant2].map(TenantPresenter.toHttp),
      });

      const response2 = await app.inject({
        method: "GET",
        url: PATHS.root,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        query: { page: "1", limit: "1" },
      });

      expect(response2.statusCode).toBe(200);
      expect(response2.json()).toStrictEqual({
        page: 1,
        limit: 1,
        hasMore: true,
        items: [TenantPresenter.toHttp(tenant)],
      });

      const response3 = await app.inject({
        method: "GET",
        url: PATHS.root,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        query: { page: "2", limit: "1" },
      });

      expect(response3.statusCode).toBe(200);
      expect(response3.json()).toStrictEqual({
        page: 2,
        limit: 1,
        hasMore: false,
        items: [TenantPresenter.toHttp(tenant2)],
      });

      const response4 = await app.inject({
        method: "GET",
        url: PATHS.root,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        query: { page: "3", limit: "1" },
      });

      expect(response4.statusCode).toBe(200);
      expect(response4.json()).toStrictEqual({
        page: 3,
        limit: 1,
        items: [],
        hasMore: false,
      });
    });

    it("should be able to throw error if payload is invalid (400)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "GET",
        url: PATHS.root,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        query: { page: "-1", limit: "101" },
      });

      expect(response.statusCode).toBe(400);

      expect(response.json()).toEqual({
        message:
          "Muito pequeno: esperado que number fosse >=1, Muito grande: esperado que number fosse <=100",
      });
    });

    it("should be able to throw error if does not authenticated (401)", async () => {
      const response = await app.inject({
        method: "GET",
        url: PATHS.root,
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe("GET /admin/tenants/:tenantId", () => {
    it("should be able to get a tenant by id (200)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "GET",
        url: PATHS.show(tenant.id.toValue()),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      expect(response.statusCode).toBe(200);

      expect(response.json()).toStrictEqual(TenantPresenter.toHttp(tenant));
    });

    it("should be able to throw error if payload is invalid (400)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "GET",
        url: PATHS.show("invalid-id"),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      expect(response.statusCode).toBe(400);

      expect(response.json()).toEqual({
        message: "Id inválido",
      });
    });

    it("should be able to throw error if does not authenticated (401)", async () => {
      const response = await app.inject({
        method: "GET",
        url: PATHS.show(tenant.id.toValue()),
      });

      expect(response.statusCode).toBe(401);
    });

    it("should be able to throw error if tenant not found (404)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "GET",
        url: PATHS.show("64b64c4f4f4f4f4f4f4f4f4f"),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      expect(response.statusCode).toBe(404);

      expect(response.json()).toEqual({
        message: "Empresa não encontrada",
      });
    });
  });

  describe("POST /admin/tenants", () => {
    it("should be able to create a tenant (201)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "POST",
        url: PATHS.create,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "Tenant 1" },
      });

      expect(response.statusCode).toBe(201);

      const responseBody = response.json();

      expect(responseBody).toHaveProperty("id");
      expect(responseBody).toHaveProperty("name", "Tenant 1");
      expect(responseBody).toHaveProperty("createdAt");
      expect(responseBody).toHaveProperty("updatedAt");

      const tenantInDb = await tenantRepository.findById({
        id: responseBody.id,
      });

      expect(tenantInDb).toEqual(
        expect.objectContaining({
          id: new UniqueEntityObjectId(responseBody.id),
          name: "Tenant 1",
          createdAt: new Date(responseBody.createdAt),
          updatedAt: new Date(responseBody.updatedAt),
        })
      );
    });

    it("should be able to throw error if payload is invalid (400)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "POST",
        url: PATHS.create,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "" },
      });

      expect(response.statusCode).toBe(400);

      expect(response.json()).toEqual({
        message: "Muito pequeno: esperado que string tivesse >=3 caracteres",
      });

      const response2 = await app.inject({
        method: "POST",
        url: PATHS.create,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "Te" },
      });

      expect(response2.statusCode).toBe(400);

      expect(response2.json()).toEqual({
        message: "Muito pequeno: esperado que string tivesse >=3 caracteres",
      });

      const response3 = await app.inject({
        method: "POST",
        url: PATHS.create,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "T".repeat(256) },
      });

      expect(response3.statusCode).toBe(400);

      expect(response3.json()).toEqual({
        message: "Muito grande: esperado que string tivesse <=255 caracteres",
      });
    });

    it("should be able to throw error if does not authenticated (401)", async () => {
      const response = await app.inject({
        method: "POST",
        url: PATHS.create,
        payload: { name: "Tenant 1" },
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe("PUT /admin/tenants/:tenantId", () => {
    it("should be able to update a tenant (200)", async () => {
      const accessToken = jwtCrypter.encrypt(
        {
          sub: account.id.toString(),
        },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "PUT",
        url: PATHS.update(tenant.id.toValue()),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "Tenant 1" },
      });

      expect(response.statusCode).toBe(200);
      expect(response.json()).toHaveProperty("id", tenant.id.toValue());
      expect(response.json()).toHaveProperty("name", "Tenant 1");
      expect(response.json()).toHaveProperty("createdAt");
      expect(response.json()).toHaveProperty("updatedAt");

      const tenantInDb = await tenantRepository.findById({
        id: tenant.id.toValue(),
      });

      expect(tenantInDb).toStrictEqual(
        expect.objectContaining({
          id: tenant.id,
          name: "Tenant 1",
          createdAt: tenant.createdAt,
          updatedAt: new Date(response.json().updatedAt),
        })
      );
    });

    it("should be able to throw error if payload is invalid (400)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "PUT",
        url: PATHS.update(tenant.id.toValue()),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "" },
      });

      expect(response.statusCode).toBe(400);

      expect(response.json()).toEqual({
        message: "Muito pequeno: esperado que string tivesse >=3 caracteres",
      });

      const response2 = await app.inject({
        method: "PUT",
        url: PATHS.update(tenant.id.toValue()),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "Te" },
      });

      expect(response2.statusCode).toBe(400);

      expect(response2.json()).toEqual({
        message: "Muito pequeno: esperado que string tivesse >=3 caracteres",
      });

      const response3 = await app.inject({
        method: "PUT",
        url: PATHS.update(tenant.id.toValue()),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "T".repeat(256) },
      });

      expect(response3.statusCode).toBe(400);

      expect(response3.json()).toEqual({
        message: "Muito grande: esperado que string tivesse <=255 caracteres",
      });
    });

    it("should be able to throw error if does not authenticated (401)", async () => {
      const response = await app.inject({
        method: "PUT",
        url: PATHS.update(tenant.id.toValue()),
        payload: { name: "Tenant 1" },
      });

      expect(response.statusCode).toBe(401);
    });

    it("should be able to throw error if tenant not found (404)", async () => {
      const accessToken = jwtCrypter.encrypt(
        { sub: account.id.toString() },
        TokenType.ACCESS_TOKEN
      );

      const response = await app.inject({
        method: "PUT",
        url: PATHS.update("64b64c4f4f4f4f4f4f4f4f4f"),
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        payload: { name: "Tenant 1" },
      });

      expect(response.statusCode).toBe(404);

      expect(response.json()).toEqual({
        message: "Empresa não encontrada",
      });
    });
  });
});

const PATHS = {
  root: "/admin/tenants",
  show: (id: string) => `/admin/tenants/${id}`,
  create: "/admin/tenants",
  update: (id: string) => `/admin/tenants/${id}`,
};
