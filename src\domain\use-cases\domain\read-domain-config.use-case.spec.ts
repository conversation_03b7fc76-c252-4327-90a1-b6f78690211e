import { ReadDomainConfigUseCase } from "./read-domain-config.use-case";

describe("ReadDomainConfigUseCase", () => {
  let useCase: ReadDomainConfigUseCase;

  beforeEach(() => {
    useCase = new ReadDomainConfigUseCase();
  });

  it("should return default response when no config file exists", async () => {
    const result = await useCase.execute();

    expect(result).toEqual({
      isBlocked: false,
      domains: [],
      hasConfig: false,
    });
  });

  // Additional test cases can be added here for parsing configurations
  // when the jest configuration is properly set up in the project
});
