// import axiosInstance from 'src/lib/axios';

// export class MagazordOrderRepository {
//   static async getOrders(
//     companyId: number,
//     page: number,
//     limit: number,
//     queries: MagazordOrderFindManyParams
//   ) {
//     const { data } = await axiosInstance.get<
//       IPagination<{ id: string }[], { totalValue: number; averageDaysDifference: number }>
//     >(`/companies/${companyId}/integrations/magazord/orders`, {
//       params: {
//         page,
//         limit,
//         ...queries,
//       },
//     });

//     return data;
//   }
//   static async getOrdersTotalValue(companyId: number, queries: MagazordOrderFindManyParams) {
//     const { data } = await axiosInstance.get<{ totalValue: number }>(
//       `/companies/${companyId}/integrations/magazord/orders/total-value`,
//       {
//         params: {
//           ...queries,
//         },
//       }
//     );

//     return data.totalValue;
//   }
//   static async getOrderByIds(companyId: number, orderIds: string[]) {
//     const { data } = await axiosInstance.get<IMagazordOrderDetailV2[]>(
//       `/companies/${companyId}/integrations/magazord/orders/details`,
//       {
//         params: {
//           ids: orderIds,
//         },
//       }
//     );

//     return data;
//   }

//   static async updateSeller(companyId: number, orderId: string, ticketId: number) {
//     await axiosInstance.put(
//       `/companies/${companyId}/integrations/magazord/orders/${orderId}/seller`,
//       {
//         ticketId,
//       }
//     );
//   }

//   static async ManageOrderSeller(companyId: number, orderId: string, userId: number | null) {
//     await axiosInstance.put(
//       `/companies/${companyId}/integrations/magazord/orders/${orderId}/manage-seller`,
//       {
//         userId,
//       }
//     );
//   }

//   static async show(companyId: number, orderId: string) {
//     const { data } = await axiosInstance.get<IMagazordOrderDetailV2>(
//       `/companies/${companyId}/integrations/magazord/orders/${orderId}`
//     );

//     return data;
//   }

//   static async getAllContactOrders(companyId: number, contactId: number) {
//     const { data } = await axiosInstance.get<IMagazordOrderClientV2[]>(
//       `/companies/${companyId}/integrations/magazord/orders/contact-orders/${contactId}`
//     );

//     return data;
//   }

//   static async statuses(companyId: number) {
//     const { data } = await axiosInstance.get<number[]>(
//       `/companies/${companyId}/integrations/magazord/orders/filters/statuses`
//     );

//     return data;
//   }

//   static async carries(companyId: number) {
//     const { data } = await axiosInstance.get<string[]>(
//       `/companies/${companyId}/integrations/magazord/orders/filters/carriers`
//     );

//     return data;
//   }

//   static async toggleCheckbox(magazordOrderId: string, companyId: number) {
//     await axiosInstance.put(
//       `/companies/${companyId}/integrations/magazord/orders/toggle-checkbox`,
//       {
//         magazordOrderId,
//       }
//     );
//   }
// }
