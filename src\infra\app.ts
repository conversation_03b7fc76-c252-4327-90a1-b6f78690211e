import "reflect-metadata";
import "@/infra/container";
import "@/core/utils/zod-methods";

import fastify from "fastify";
import cors from "@fastify/cors";

import { adminRoutes } from "@/infra/http/routes";
import { fastifyErrorHandler } from "@/infra/http/fastify/error-handler";

export const app = fastify({
  logger: false,
});

app.register(cors, {
  credentials: true,
});

// ERROR HANDLER
app.setErrorHandler(fastifyErrorHandler);

adminRoutes.forEach((route) => {
  app.register(route.route, { prefix: `/v1${route.path}` });
});
