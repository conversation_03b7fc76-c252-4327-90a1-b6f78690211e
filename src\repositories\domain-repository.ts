import axiosInstance from 'src/lib/axios';

export class DomainRepository {
  static async handleDomainList(domainData: { isBlocked: boolean; domains: { url: string }[] }) {
    const { data } = await axiosInstance.post<{
      isBlocked: boolean;
      domains: { url: string }[];
    }>(`/v1/domain`, {
      isBlocked: domainData.isBlocked,
      domains: domainData.domains.map((domain) => ({ url: domain.url })),
    });

    return data;
  }
}
