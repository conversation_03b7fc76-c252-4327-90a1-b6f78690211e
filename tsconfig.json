{"compilerOptions": {"target": "ES2020", "module": "commonjs", "strict": true, "skipLibCheck": true, "esModuleInterop": true, "strictNullChecks": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "rootDir": ".", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/tests/*": ["tests/*"]}, "types": ["vitest/globals"]}, "include": ["src", "tests"], "exclude": ["node_modules", "dist"]}