// import { container } from "tsyringe";
// import {
//   ITenantRepository,
// } from "@/domain/repositories";
// import { DependencyInjectionEnum } from "@/core/enums/dependency-injection";

// // these container must be registered but not in singleton mode because the repositories are injectable and receive the UnitOfWork that is in transient scope
// // so, if we register in singleton the repositories will receive always the same instance of UnitOfWork that is null initially (unit of work are only injected after beginTransaction is called)
// // and this will cause problems when we try to use transactions

// container.register<IProjectRepository>(
//   DependencyInjectionEnum.PROJECT_REPOSITORY,
//   MongooseProjectRepository
// );

// container.register<ITenantRepository>(
//   DependencyInjectionEnum.TENANT_REPOSITORY,
//   MongooseTenantRepository
// );

// container.register<IAccountRepository>(
//   DependencyInjectionEnum.ACCOUNT_REPOSITORY,
//   MongooseAccountRepository
// );

// container.register<IProductRepository>(
//   DependencyInjectionEnum.PRODUCT_REPOSITORY,
//   MongooseProductRepository
// );

// container.register<IPriceRepository>(
//   DependencyInjectionEnum.PRICE_REPOSITORY,
//   MongoosePriceRepository
// );

// container.register<ISubscriptionRepository>(
//   DependencyInjectionEnum.SUBSCRIPTION_REPOSITORY,
//   MongooseSubscriptionRepository
// );

// container.register<IBillingRepository>(
//   DependencyInjectionEnum.BILLING_REPOSITORY,
//   MongooseBillingRepository
// );

// container.register<IInvoiceRepository>(
//   DependencyInjectionEnum.INVOICE_REPOSITORY,
//   MongooseInvoiceRepository
// );

// container.register<IInvoiceLineRepository>(
//   DependencyInjectionEnum.INVOICE_LINE_REPOSITORY,
//   MongooseInvoiceLineRepository
// );
