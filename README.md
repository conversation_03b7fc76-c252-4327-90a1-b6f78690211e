# TCC Backend - DNSmasq Domain Management

This backend application provides APIs for managing domain blocking/allowing configurations using DNSmasq.

## API Endpoints

### Domain Management

#### POST `/domains`

Creates/updates the DNSmasq configuration file based on provided domains and blocking mode.

**Request Body:**

```json
{
  "isBlocked": boolean,
  "domains": [
    {
      "url": "string (valid URL)"
    }
  ]
}
```

**Response:**

```json
{
  "config": "string (generated dnsmasq configuration)",
  "filePath": "string (path to the created config file)"
}
```

**Behavior:**

- `isBlocked: true`: Blocks all domains by default, allows specified domains
- `isBlocked: false`: Allows all domains by default, blocks specified domains
- Creates a `dnsmasq.conf` file in the `config/` directory

#### GET `/domains/config`

Reads the current DNSmasq configuration and returns the parsed domain list.

**Response:**

```json
{
  "isBlocked": boolean,
  "hasConfig": boolean,
  "domains": [
    {
      "url": "string",
      "status": "blocked" | "allowed"
    }
  ]
}
```

**Behavior:**

- Returns current configuration state from `config/dnsmasq.conf`
- If no config file exists, returns `hasConfig: false` with empty domains array
- Parses the dnsmasq configuration to extract domain rules and their statuses

## Project Structure

The project follows a clean architecture pattern:

- `src/domain/` - Business logic and use cases
- `src/infra/` - Infrastructure layer (HTTP, database, etc.)
- `src/core/` - Shared utilities and base classes

### Key Files for Domain Management

- `src/domain/use-cases/domain/handle-domain-list.use-case.ts` - Creates dnsmasq config
- `src/domain/use-cases/domain/read-domain-config.use-case.ts` - Reads and parses config
- `src/infra/http/controllers/domain.controller.ts` - HTTP endpoints
- `src/infra/http/routes/domain.routes.ts` - Route definitions

## Configuration File Format

The generated DNSmasq configuration follows these patterns:

### Blocked Mode (isBlocked: true)

```bash
# Modo bloqueado - Bloqueia tudo por padrão
address=/#/0.0.0.0
address=/#/::

# Domínios liberados
server=/example.com/*******
server=/allowed-site.com/*******
```

### Allowed Mode (isBlocked: false)

```bash
# Modo liberado - Permite tudo por padrão
address=/#/*******
address=/#/*******

# Domínios bloqueados
server=/blocked-site.com/0.0.0.0
server=/unwanted-domain.com/0.0.0.0
```
