import { HandleDomainListUseCase } from "@/domain/use-cases/domain/handle-domain-list.use-case";
import { inject, injectable } from "tsyringe";

interface DomainItem {
  url: string;
}

interface FormData {
  isBlocked: boolean;
  domains: DomainItem[];
}

interface IHandleDomainListServiceResponse {
  config: string;
}

@injectable()
export class HandleDomainListService {
  constructor(
    @inject(HandleDomainListUseCase)
    private handleDomainListUseCase: HandleDomainListUseCase
  ) {}

  async execute(formData: FormData): Promise<IHandleDomainListServiceResponse> {
    const { config } = await this.handleDomainListUseCase.execute(
      formData
    );

    return { config };
  }
}
