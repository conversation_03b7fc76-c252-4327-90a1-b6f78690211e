import "reflect-metadata";
import "module-alias/register";
import "src/infra/utils/module-alias"; // this must be the first import and without alias
import "@/infra/container";

import * as z from "zod";
import { pt } from "zod/locales";

z.config(pt());

import { app } from "@/infra/app";
import { env } from "@/infra/env";
import { injectable } from "tsyringe";
import { logger } from "@/core/logger";
import GracefulShutdown from "http-graceful-shutdown";

@injectable()
class Main {
  constructor() {}

  async run() {
    try {
      const address = await app.listen({ port: env.PORT });

      GracefulShutdown(app.server, {
        finally: async () => {
          logger.info("Graceful Shutdown - Closing connections...");
        },
      });

      logger.info(`Server listening on ${address} 🚀`);
    } catch (error) {
      logger.error(error);

      process.exit(1);
    }
  }
}

(async () => {
  const main = new Main();

  await main.run();
})();
