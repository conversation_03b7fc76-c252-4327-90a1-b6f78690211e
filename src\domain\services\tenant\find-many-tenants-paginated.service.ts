import { DependencyInjectionEnum } from "@/core/enums/dependency-injection";
import { Metadata } from "@/core/types/metadata";
import { Tenant } from "@/domain/entities/tenant";
import { ITenantRepository } from "@/domain/repositories";
import { inject, injectable } from "tsyringe";

export interface IFindManyTenantsPaginatedServiceRequest {
  filters?: {
    page?: number;
    limit?: number;
  };
}

export interface IFindManyTenantsPaginatedServiceResponse {
  metadata: Metadata<Tenant>;
}

@injectable()
export class FindManyTenantsPaginatedService {
  constructor(
    @inject(DependencyInjectionEnum.TENANT_REPOSITORY)
    private readonly tenantRepository: ITenantRepository
  ) {}

  async execute({
    filters,
  }: IFindManyTenantsPaginatedServiceRequest): Promise<IFindManyTenantsPaginatedServiceResponse> {
    const { page = 1, limit = 100 } = filters || {};

    const metadata = await this.tenantRepository.findManyPaginated({
      page,
      limit,
    });

    return { metadata };
  }
}
