import { inject, injectable } from "tsyringe";
import { FastifyReply, FastifyRequest } from "fastify";
import { DomainFastifyRequest } from "@/infra/http/routes/domain.routes";
import { HandleDomainListService } from "@/domain/services/domain/handle-domain-list.service";
import { ReadDomainConfigService } from "@/domain/services/domain/read-domain-config.service";

@injectable()
export class DomainController {
  constructor(
    @inject(HandleDomainListService)
    private readonly handleDomainListService: HandleDomainListService,
    @inject(ReadDomainConfigService)
    private readonly readDomainConfigService: ReadDomainConfigService
  ) {}

  handleDomainList = async (
    req: FastifyRequest<DomainFastifyRequest.HandleDomainList>,
    reply: FastifyReply
  ) => {
    const { config } = await this.handleDomainListService.execute({
      domains: req.body.domains,
      isBlocked: req.body.isBlocked,
    });

    return reply.status(200).send({ config });
  };

  readDomainConfig = async (req: FastifyRequest, reply: FastifyReply) => {
    const result = await this.readDomainConfigService.execute();
    return reply.status(200).send(result);
  };
}
