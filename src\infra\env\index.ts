import { treeifyError, z } from "zod";
import { logger } from "@/core/logger";
import { BadRequestError } from "@/core/errors/bad-request.error";

const envSchema = z.object({
  NODE_ENV: z.enum(["development", "production", "staging", "test"]),
  PORT: z.coerce
    .number()
    .int()
    .positive({ message: "PORT é obrigatório" })
    .default(3333),
  TZ: z.string().default("America/Sao_Paulo"),
});

const envParsed = envSchema.safeParse(process.env);

if (!envParsed.success) {
  const errorMessage = treeifyError(envParsed.error);

  logger.info(errorMessage, "❌ Invalid environment variables:");
  throw new BadRequestError(errorMessage.errors.join(", "));
}

export const env = envParsed.data;
