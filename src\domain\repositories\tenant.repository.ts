import { Tenant } from "@/domain/entities/tenant";
import { Metadata } from "@/core/types/metadata";

export interface ITenantRepository {
  create(data: Tenant): Promise<Tenant>;
  update(data: Tenant): Promise<Tenant>;

  findMany(): Promise<Tenant[]>;

  findManyPaginated({}: {
    page?: number;
    limit?: number;
  }): Promise<Metadata<Tenant>>;

  findById({ id }: { id: string }): Promise<Tenant | null>;
}
