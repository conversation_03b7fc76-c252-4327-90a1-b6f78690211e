import { FastifyReply, FastifyRequest } from "fastify";

import { UnprocessableEntityError } from "@/core/errors/unprocessable-entity.error";
import { DuplicateResourceError } from "@/core/errors/duplicate-resource.error";
import { BadRequestError } from "@/core/errors/bad-request.error";
import { UnauthorizedError } from "@/core/errors/unauthorized.error";
import { ForbiddenError } from "@/core/errors/forbidden.error";
import { ResourceNotFoundError } from "@/core/errors/resource-not-found.error";
import { env } from "@/infra/env";
import { logger } from "@/core/logger";

export async function fastifyErrorHandler(
  error: Error,
  _: FastifyRequest,
  reply: FastifyReply
) {
  if (env.NODE_ENV !== "test") {
    logger.error(error);
  }

  if (error instanceof DuplicateResourceError) {
    return reply.status(409).send({ message: error.message });
  }

  if (error instanceof BadRequestError) {
    return reply.status(400).send({ message: error.message });
  }

  if (error instanceof UnauthorizedError) {
    return reply.status(401).send({ message: error.message });
  }

  if (error instanceof ForbiddenError) {
    return reply.status(403).send({ message: error.message });
  }

  if (error instanceof ResourceNotFoundError) {
    return reply.status(404).send({ message: error.message });
  }

  if (error instanceof UnprocessableEntityError) {
    return reply.status(422).send({ message: error.message });
  }

  return reply
    .status(500)
    .send({ message: error.message ?? "Internal server error" });
}
