import { injectable } from "tsyringe";
import { readFile } from "fs/promises";
import { join } from "path";

interface DomainInfo {
  url: string;
  status: "blocked" | "allowed";
}

interface IReadDomainConfigUseCaseResponse {
  isBlocked: boolean;
  domains: DomainInfo[];
  hasConfig: boolean;
}

@injectable()
export class ReadDomainConfigUseCase {
  async execute(): Promise<IReadDomainConfigUseCaseResponse> {
    const configDir = join(process.cwd(), "config");
    const filePath = join(configDir, "dnsmasq.conf");

    try {
      const configContent = await readFile(filePath, { encoding: "utf-8" });
      return this.parseConfig(configContent);
    } catch (error) {
      console.log(
        "No existing dnsmasq config found or error reading file:",
        error
      );
      return {
        isBlocked: false,
        domains: [],
        hasConfig: false,
      };
    }
  }

  private parseConfig(configContent: string): IReadDomainConfigUseCaseResponse {
    const lines = configContent.split("\n").map((line) => line.trim());

    // Determine if it's in blocked mode by checking the default addresses
    const isBlocked = lines.some(
      (line) => line === "address=/#/0.0.0.0" || line === "address=/#/::"
    );

    const domains: DomainInfo[] = [];

    // Extract domain entries
    lines.forEach((line) => {
      if (line.startsWith("server=/") && line.includes("/")) {
        const match = line.match(/^server=\/([^\/]+)\/(.*)/);
        if (match) {
          const domain = match[1];
          const server = match[2];

          // Skip if it's a wildcard (#) or empty domain
          if (domain === "#" || !domain) {
            return;
          }

          // Determine status based on server IP
          let status: "blocked" | "allowed";
          if (isBlocked) {
            // In blocked mode, domains with non-zero IPs are allowed
            status =
              server === "*******" || server === "*******"
                ? "allowed"
                : "blocked";
          } else {
            // In allowed mode, domains with zero IPs are blocked
            status =
              server === "0.0.0.0" || server === "::" ? "blocked" : "allowed";
          }

          domains.push({
            url: domain,
            status,
          });
        }
      }
    });

    return {
      isBlocked,
      domains,
      hasConfig: true,
    };
  }
}
