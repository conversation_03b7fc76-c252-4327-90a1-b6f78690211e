import { HandleDomainListUseCase } from "./handle-domain-list.use-case";

interface DomainItem {
  url: string;
}

interface FormData {
  isBlocked: boolean;
  domains: DomainItem[];
}

describe("HandleDomainListUseCase", () => {
  let useCase: HandleDomainListUseCase;

  beforeEach(() => {
    useCase = new HandleDomainListUseCase();
  });

  describe("when isBlocked is true", () => {
    it("should generate config that blocks everything by default and allows specific domains", async () => {
      const formData: FormData = {
        isBlocked: true,
        domains: [{ url: "google.com" }, { url: "github.com" }],
      };

      const result = await useCase.execute(formData);

      expect(result).toContain("# Modo bloqueado - Bloqueia tudo por padrão");
      expect(result).toContain("address=/#/0.0.0.0");
      expect(result).toContain("address=/#/::");
      expect(result).toContain("server=/google.com/*******");
      expect(result).toContain("server=/github.com/*******");
    });

    it("should generate config with only blocking rules when no domains provided", async () => {
      const formData: FormData = {
        isBlocked: true,
        domains: [],
      };

      const result = await useCase.execute(formData);

      expect(result).toContain("# Modo bloqueado - Bloqueia tudo por padrão");
      expect(result).toContain("address=/#/0.0.0.0");
      expect(result).toContain("address=/#/::");
      expect(result).not.toContain("server=/");
    });
  });

  describe("when isBlocked is false", () => {
    it("should generate config that allows everything by default and blocks specific domains", async () => {
      const formData: FormData = {
        isBlocked: false,
        domains: [{ url: "facebook.com" }, { url: "twitter.com" }],
      };

      const result = await useCase.execute(formData);

      expect(result).toContain("# Modo liberado - Permite tudo por padrão");
      expect(result).toContain("address=/#/*******");
      expect(result).toContain("address=/#/*******");
      expect(result).toContain("server=/facebook.com/0.0.0.0");
      expect(result).toContain("server=/twitter.com/0.0.0.0");
    });

    it("should generate config with only allowing rules when no domains provided", async () => {
      const formData: FormData = {
        isBlocked: false,
        domains: [],
      };

      const result = await useCase.execute(formData);

      expect(result).toContain("# Modo liberado - Permite tudo por padrão");
      expect(result).toContain("address=/#/*******");
      expect(result).toContain("address=/#/*******");
      expect(result).not.toContain("server=/");
    });
  });
});
