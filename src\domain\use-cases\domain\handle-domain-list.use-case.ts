import { injectable } from "tsyringe";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";

interface DomainItem {
  url: string;
}

interface FormData {
  isBlocked: boolean;
  domains: DomainItem[];
}

interface IHandleDomainListUseCaseResponse {
  config: string;
}

@injectable()
export class HandleDomainListUseCase {
  async execute(formData: FormData): Promise<IHandleDomainListUseCaseResponse> {
    const { isBlocked, domains } = formData;

    let config = "";

    if (isBlocked) {
      config += "# Modo bloqueado - Bloqueia tudo por padrão\n";
      config += "address=/#/0.0.0.0\n";
      config += "address=/#/::\n";
      config += "\n";

      if (domains && domains.length > 0) {
        config += "# Domínios liberados\n";
        domains.forEach((domain) => {
          config += `server=/${domain.url}/*******\n`;
        });
      }
    } else {
      // Modo liberado: libera tudo por padrão e bloqueia domínios específicos
      config += "# Modo liberado - Permite tudo por padrão\n";
      config += "address=/#/*******\n";
      config += "address=/#/*******\n";
      config += "\n";

      if (domains && domains.length > 0) {
        config += "# Domínios bloqueados\n";
        domains.forEach((domain) => {
          config += `server=/${domain.url}/0.0.0.0\n`;
        });
      }
    }

    // Create config directory if it doesn't exist and write the dnsmasq config file
    const configDir = join(process.cwd(), "config");
    const filePath = join(configDir, "dnsmasq.conf");

    try {
      // Ensure config directory exists
      await mkdir(configDir, { recursive: true });

      // Write the config to file
      await writeFile(filePath, config, { encoding: "utf-8" });
      console.log(`DNSmasq config written to: ${filePath}`);
    } catch (error) {
      console.error("Error writing dnsmasq config file:", error);
      throw new Error("Failed to write dnsmasq configuration file");
    }

    return { config };
  }
}
