import { ReadDomainConfigUseCase } from "@/domain/use-cases/domain/read-domain-config.use-case";
import { inject, injectable } from "tsyringe";

interface DomainInfo {
  url: string;
  status: "blocked" | "allowed";
}

interface IReadDomainConfigServiceResponse {
  isBlocked: boolean;
  domains: DomainInfo[];
  hasConfig: boolean;
}

@injectable()
export class ReadDomainConfigService {
  constructor(
    @inject(ReadDomainConfigUseCase)
    private readDomainConfigUseCase: ReadDomainConfigUseCase
  ) {}

  async execute(): Promise<IReadDomainConfigServiceResponse> {
    const result = await this.readDomainConfigUseCase.execute();
    return result;
  }
}
