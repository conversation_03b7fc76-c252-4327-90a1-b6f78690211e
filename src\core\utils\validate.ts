import { ZodType } from "zod";
import { fromZodError } from "zod-validation-error";
import { BadRequestError } from "@/core/errors/bad-request.error";

export function validate<T>(
  schema: ZodType<T>,
  data: unknown,
  error?: Error
): void {
  const result = schema.safeParse(data);

  if (!result.success) {
    throw new BadRequestError(
      fromZodError(result.error, {
        includePath: false,
        forceTitleCase: false,
        issueSeparator: ", ",
        unionSeparator: " ou ",
        prefix: null,
        prefixSeparator: "",
      }).toString()
    );
  }
}
